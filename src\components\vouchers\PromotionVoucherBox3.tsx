import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  Pagination,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import TitleDialog from "../dialog/TitleDialog";
import SearchCategoryPromotion, { Category } from "./promotions/SearchCategoryPromotion";
import { CategoryType } from "@/src/api/types/product-category.types";
import BoxImage from "../box-image";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import SearchItemsPromotion, {
  ProductVoucherPromotionType,
} from "./promotions/SearchItemsPromotion";
import CustomerConditionPromotion from "./promotions/CustomerConditionPromotion";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useStoreId } from "@/src/hooks/use-store-id";
import { LIMIT_TYPE } from "@/src/api/types/voucher.type";

export default function PromotionVoucherBox3({
  voucher,
  type = "voucher",
  showProductSelection = true,
}) {
  const {
    setValue,
    getValues,
    watch,
    control,
    formState: { errors },
  } = useFormContext<any>();
  const limitType = watch("limitType");
  const [chooseText, setChooseText] = useState(null);

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<Category[]>([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [currentProductGroupByCode, setCurrentProductGroupByCode] = useState({});
  const [currentData, setCurrentData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 5;

  const totalRecords = Object.entries(currentProductGroupByCode || {}).length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);

  const { getProductCategoryDetail } = useProductCategory();
  const { listItemsByItemsIds } = useVoucher();
  const storeId = useStoreId();

  const handlePageChange = (event, pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleChooseLimitType = () => {
    setOpenDialog(true);
  };

  const handleSubmitCategory = async (items) => {
    setSelectedCategories(items);
    setValue(
      "categoryIds",
      items.map((cat) => cat.categoryId)
    );
    handleCloseDialog();
  };

  const removeCategory = (categoryId) => {
    const updated = selectedCategories.filter((cat) => cat.categoryId !== categoryId);
    setSelectedCategories(updated);
    setValue(
      "categoryIds",
      updated.map((cat) => cat.categoryId)
    );
  };

  const deleteProduct = (itemsCode) => {
    const listItems = selectedProducts.filter((item) => item.itemsCode !== itemsCode);

    const groupedData = Object.groupBy(listItems, (item) => item.itemsCode);
    setSelectedProducts(listItems);
    setValue(
      "productIds",
      listItems.map((item) => item.itemsId)
    );

    if (
      currentPage > 1 &&
      Object.entries(groupedData || {}).length <= currentPage * recordsPerPage - recordsPerPage
    ) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleSubmitProducts = async (products: ProductVoucherPromotionType[]) => {
    const filteredItems = products.filter(
      (newItem) => !selectedProducts.some((product) => product.itemsId === newItem.itemsId)
    );

    if (filteredItems.length > 0) {
      setSelectedProducts((prevState) => {
        return [...prevState, ...filteredItems];
      });

      const currentProductIds = getValues("productIds") || [];
      const newProductIds = filteredItems.map((item) => item.itemsId);

      setValue("productIds", [...currentProductIds, ...newProductIds]);
    }
    handleCloseDialog();
  };

  useEffect(() => {
    const groupedData = Object.groupBy(selectedProducts, (item) => item.itemsCode);
    setCurrentProductGroupByCode(groupedData);
  }, [selectedProducts]);

  useEffect(() => {
    if (limitType === LIMIT_TYPE.CATEGORY) {
      setChooseText("Chọn danh mục sản phẩm");
    } else if (limitType === LIMIT_TYPE.PRODUCT) {
      setChooseText("Chọn sản phẩm");
    } else {
      setChooseText(null);
    }
  }, [limitType]);

  useEffect(() => {
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = currentPage * recordsPerPage;
    setCurrentData(Object.entries(currentProductGroupByCode || {}).slice(startIndex, endIndex));
  }, [currentPage, currentProductGroupByCode]);

  const fetchCategoryDetail = async (categoryId) => {
    const response = await getProductCategoryDetail(categoryId);
    if (response?.data) {
      setSelectedCategories([response.data]);
    }
  };

  const fetchProductByItemsIds = async (productIds, shopId) => {
    const response = await listItemsByItemsIds({ shopId: shopId, itemsIds: productIds });
    if (response?.data?.data) {
      setSelectedProducts(response?.data?.data);
    }
  };

  useEffect(() => {
    if (voucher && voucher.productIds && storeId) {
      fetchProductByItemsIds(voucher.productIds, storeId);
    } else {
      // Reset selectedProducts when voucher doesn't have productIds
      setSelectedProducts([]);
    }
  }, [voucher, storeId]);

  useEffect(() => {
    async function fetchCategoriesDetail(ids) {
      const results = await Promise.all(
        ids.map(async (id) => {
          const res = await getProductCategoryDetail(id);
          return res?.data || { categoryId: id };
        })
      );
      setSelectedCategories(results);
    }
    if (voucher && voucher.categoryIds && voucher.categoryIds.length > 0) {
      fetchCategoriesDetail(voucher.categoryIds);
    } else {
      setSelectedCategories([]);
    }
  }, [voucher]);

  return (
    <Box>
      {showProductSelection && (
        <>
          <Typography marginBottom={3} variant="h6">
            Giới hạn sử dụng
          </Typography>
          {type === "voucher" && (
            <Box>
              <FormControl>
                <FormLabel>Áp dụng cho</FormLabel>
                <Controller
                  name="limitType"
                  control={control}
                  rules={{ required: "Please select an option" }}
                  render={({ field }) => (
                    <RadioGroup {...field}>
                      <FormControlLabel
                        value={LIMIT_TYPE.ALL}
                        control={<Radio />}
                        label="Tất cả sản phẩm"
                      />
                      <FormControlLabel
                        value={LIMIT_TYPE.CATEGORY}
                        control={<Radio />}
                        label="Chỉ định danh mục sản phẩm"
                      />
                      <FormControlLabel
                        value={LIMIT_TYPE.PRODUCT}
                        control={<Radio />}
                        label="Chỉ định sản phẩm"
                      />
                    </RadioGroup>
                  )}
                />
                {errors.limitType && (
                  <span style={{ color: "red" }}>{errors.limitType.message}</span>
                )}
              </FormControl>
              {chooseText && (
                <>
                  <Box
                    sx={{
                      backgroundColor: "neutral.50",
                      padding: 2,
                      borderRadius: 1,
                      width: "100%",
                    }}
                  >
                    <Button onClick={handleChooseLimitType}>
                      {limitType === LIMIT_TYPE.CATEGORY
                        ? selectedCategories.length > 0
                          ? "Đổi danh mục"
                          : chooseText
                        : chooseText}
                    </Button>
                  </Box>
                  <Box>
                    {limitType === LIMIT_TYPE.CATEGORY && selectedCategories.length > 0 && (
                      <Box marginTop={1}>
                        {selectedCategories.map((cat) => (
                          <Box
                            key={cat.categoryId}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                            mb={1}
                          >
                            <BoxImage link={cat?.image?.link} alt={cat.categoryName}>
                              <Typography>{cat.categoryName || cat.categoryId}</Typography>
                            </BoxImage>
                            <IconButton onClick={() => removeCategory(cat.categoryId)}>
                              <DeleteOutlineOutlinedIcon />
                            </IconButton>
                          </Box>
                        ))}
                      </Box>
                    )}

                    {limitType === LIMIT_TYPE.CATEGORY && errors.categoryIds && (
                      <Typography variant="subtitle2" color="red" marginTop={1}>
                        {errors.categoryIds.message}
                      </Typography>
                    )}

                    {limitType === LIMIT_TYPE.PRODUCT &&
                      Object.entries(currentProductGroupByCode || {}) &&
                      Object.entries(currentProductGroupByCode || {}).length > 0 &&
                      currentData.map(([itemsCode, items]) => {
                        const item = items[0];
                        return (
                          <Box key={itemsCode}>
                            <Box
                              display="flex"
                              marginTop={2}
                              justifyContent="space-between"
                              marginBottom={1}
                            >
                              <BoxImage link={item.images?.[0].link} alt={item.itemsName}>
                                <Typography>{item.itemsName}</Typography>
                              </BoxImage>
                              <IconButton onClick={() => deleteProduct(item.itemsCode)}>
                                <DeleteOutlineOutlinedIcon />
                              </IconButton>
                            </Box>
                            <Divider />
                          </Box>
                        );
                      })}

                    {limitType === LIMIT_TYPE.PRODUCT && errors.productIds && (
                      <Typography variant="subtitle2" color="red" marginTop={1}>
                        {errors.productIds.message}
                      </Typography>
                    )}

                    {Object.entries(currentProductGroupByCode || {}).length > 0 && (
                      <Box display="flex" justifyContent="center" marginTop={2}>
                        <Pagination
                          count={totalPages}
                          page={currentPage}
                          onChange={handlePageChange}
                          color="primary"
                          shape="rounded"
                        />
                      </Box>
                    )}
                  </Box>
                  <TitleDialog
                    title={chooseText}
                    open={openDialog}
                    handleClose={handleCloseDialog}
                    submitBtnTitle="Xác nhận"
                    showActionDialog={false}
                    maxWidth="xl"
                  >
                    {limitType === LIMIT_TYPE.CATEGORY ? (
                      <SearchCategoryPromotion
                        handleSubmit={handleSubmitCategory}
                        handleClose={handleCloseDialog}
                        selectedCategories={selectedCategories}
                      />
                    ) : (
                      <SearchItemsPromotion
                        handleSubmit={handleSubmitProducts}
                        handleClose={handleCloseDialog}
                      />
                    )}
                  </TitleDialog>
                </>
              )}
            </Box>
          )}
        </>
      )}

      <Box marginTop={2}>
        <CustomerConditionPromotion voucher={voucher} />
      </Box>

      <Box marginTop={4}>
        <Typography>Sử dụng</Typography>

        <FormControl fullWidth sx={{ marginTop: 2 }}>
          <Typography gutterBottom>Tổng số lần có thể sử dụng chiết khấu</Typography>

          <Controller
            name="maxUsagePerUser"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                placeholder="Nhập số lần mỗi khách hàng được sử dụng voucher này"
                type="number"
                variant="outlined"
                error={!!errors.maxUsagePerUser}
                helperText={errors.maxUsagePerUser?.message}
                onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
              />
            )}
          />
        </FormControl>
      </Box>
    </Box>
  );
}
