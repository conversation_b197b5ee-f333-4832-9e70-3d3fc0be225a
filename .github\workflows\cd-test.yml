name: Build and Deploy Next.js to Server Testing

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build:
    name: Build
    # if: github.ref == 'refs/heads/deploy'
    runs-on: ubuntu-latest
    env:
      CI: true
      SOURCE_DIR: /var/www/admin-partner
    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"

      - name: Install dependencies
        run: |
          yarn install
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          yarn build:development
          tar -cvf deploy.tar ./build

      - name: Init folder on server ${{env.SOURCE_DIR}}
        uses: appleboy/ssh-action@master
        with:
          host: ${{secrets.STAG_REMOTE_HOST}}
          username: ${{secrets.STAG_REMOTE_USER}}
          password: ${{secrets.STAG_SSH_PASSWORD}}
          script: mkdir -p ${{env.SOURCE_DIR}}/build

      - name: Copy built files to remote servers
        uses: appleboy/scp-action@master
        with:
          host: ${{secrets.STAG_REMOTE_HOST}}
          username: ${{secrets.STAG_REMOTE_USER}}
          password: ${{secrets.STAG_SSH_PASSWORD}}
          overwrite: 1
          source: "deploy.tar" #'./dist/*'
          target: ${{env.SOURCE_DIR}}

      - name: Unzip deploy.tar on server ${{env.SOURCE_DIR}}
        uses: appleboy/ssh-action@master
        with:
          host: ${{secrets.STAG_REMOTE_HOST}}
          username: ${{secrets.STAG_REMOTE_USER}}
          password: ${{secrets.STAG_SSH_PASSWORD}}
          script: |
            tar -xvf ${{env.SOURCE_DIR}}/deploy.tar -C ${{env.SOURCE_DIR}}
            sudo ls -a
            sudo chmod +x ${{env.SOURCE_DIR}}/build
            sudo usermod -a -G root www-data
            # Ensure .htaccess is in the build directory for Apache
            if [ -f "${{env.SOURCE_DIR}}/build/.htaccess" ]; then
              echo ".htaccess file found and ready for Apache"
            fi

      - name: Send noti
        run: |

          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build AdminPartner Dev success - ${{ github.event.head_commit.message }}'
